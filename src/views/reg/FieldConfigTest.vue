<template>
  <div class="field-config-test">
    <a-card title="字段折叠控制系统重构测试" size="small">
      <!-- 测试控制面板 -->
      <div class="test-controls">
        <a-space wrap>
          <a-button type="primary" @click="runAllTests">
            <Icon icon="ant-design:play-circle-outlined" />
            运行所有测试
          </a-button>
          <a-button @click="clearTestResults">
            <Icon icon="ant-design:clear-outlined" />
            清除结果
          </a-button>
          <a-button @click="resetToDefault">
            <Icon icon="ant-design:reload-outlined" />
            重置配置
          </a-button>
          <a-button @click="clearCache">
            <Icon icon="ant-design:delete-outlined" />
            清除缓存
          </a-button>
        </a-space>
      </div>

      <!-- 测试结果显示 -->
      <div class="test-results" v-if="testResults.length > 0">
        <a-divider>测试结果</a-divider>
        <a-list size="small" :data-source="testResults">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span :class="item.status === 'success' ? 'test-success' : 'test-error'">
                    <Icon :icon="item.status === 'success' ? 'ant-design:check-circle-outlined' : 'ant-design:close-circle-outlined'" />
                    {{ item.name }}
                  </span>
                </template>
                <template #description>
                  {{ item.description }}
                  <div v-if="item.details" class="test-details">
                    <pre>{{ item.details }}</pre>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- 权限测试 -->
      <div class="permission-test">
        <a-divider>权限测试</a-divider>
        <a-descriptions size="small" :column="2">
          <a-descriptions-item label="是否管理员">
            <a-tag :color="isAdmin ? 'green' : 'red'">
              {{ isAdmin ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="可配置字段">
            <a-tag :color="canConfigureFields ? 'green' : 'red'">
              {{ canConfigureFields ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="可查看配置">
            <a-tag :color="canViewFieldConfig ? 'green' : 'red'">
              {{ canViewFieldConfig ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="可编辑配置">
            <a-tag :color="canEditFieldConfig ? 'green' : 'red'">
              {{ canEditFieldConfig ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 配置状态 -->
      <div class="config-status">
        <a-divider>配置状态</a-divider>
        <a-descriptions size="small" :column="2">
          <a-descriptions-item label="配置加载状态">
            <a-tag :color="configLoading ? 'blue' : 'green'">
              {{ configLoading ? '加载中' : '已加载' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="配置来源">
            <a-tag color="blue">{{ lastLoadSource }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="外部字段数量">
            <a-tag color="green">{{ outsideFields.length }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="折叠字段数量">
            <a-tag color="orange">{{ collapseFields.length }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="隐藏字段数量">
            <a-tag color="red">{{ hiddenFields.length }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="加载错误">
            <a-tag :color="loadError ? 'red' : 'green'">
              {{ loadError || '无错误' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 字段配置详情 -->
      <div class="field-config-details">
        <a-divider>字段配置详情</a-divider>
        <a-tabs>
          <a-tab-pane key="outside" tab="外部字段">
            <a-table 
              :columns="fieldColumns" 
              :data-source="outsideFields" 
              size="small" 
              :pagination="false"
            />
          </a-tab-pane>
          <a-tab-pane key="collapse" tab="折叠字段">
            <a-table 
              :columns="fieldColumns" 
              :data-source="collapseFields" 
              size="small" 
              :pagination="false"
            />
          </a-tab-pane>
          <a-tab-pane key="hidden" tab="隐藏字段">
            <a-table 
              :columns="fieldColumns" 
              :data-source="hiddenFields" 
              size="small" 
              :pagination="false"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>

    <!-- 表单测试区域 -->
    <a-card title="表单功能测试" size="small" style="margin-top: 16px;">
      <CustomerRegFormEnhanced 
        ref="formRef"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import CustomerRegFormEnhanced from './components/CustomerRegFormEnhanced.vue';

// 导入测试相关的模块
import { FieldKeys, FieldDisplayLocation } from './config/FieldDefinitions';
import { FormTypes, customerRegFieldManager } from './config/FieldConfigUtils';
import { useFieldConfig } from './composables/useFieldConfig';
import { useFieldConfigPermissions } from './composables/usePermissions';

// 权限管理
const {
  isAdmin,
  canConfigureFields,
  canViewFieldConfig,
  canEditFieldConfig,
} = useFieldConfigPermissions();

// 字段配置管理
const {
  isLoading: configLoading,
  fieldConfig,
  loadError,
  lastLoadSource,
  outsideFields,
  collapseFields,
  hiddenFields,
  loadFieldConfig,
  resetToDefault,
  clearCache,
} = useFieldConfig({
  formType: FormTypes.CUSTOMER_REG,
  enableCache: true,
  fallbackToDefault: true,
});

// 测试状态
const testResults = ref<Array<{
  name: string;
  status: 'success' | 'error';
  description: string;
  details?: string;
}>>([]);

const formRef = ref();

// 表格列定义
const fieldColumns = [
  {
    title: '字段键',
    dataIndex: 'fieldKey',
    key: 'fieldKey',
    width: 150,
  },
  {
    title: '字段名称',
    dataIndex: 'fieldName',
    key: 'fieldName',
    width: 120,
  },
  {
    title: '显示位置',
    dataIndex: 'displayLocation',
    key: 'displayLocation',
    width: 100,
    customRender: ({ text }: { text: string }) => {
      const colorMap = {
        [FieldDisplayLocation.OUTSIDE]: 'green',
        [FieldDisplayLocation.COLLAPSE]: 'orange',
        [FieldDisplayLocation.HIDDEN]: 'red',
      };
      const labelMap = {
        [FieldDisplayLocation.OUTSIDE]: '外部',
        [FieldDisplayLocation.COLLAPSE]: '折叠',
        [FieldDisplayLocation.HIDDEN]: '隐藏',
      };
      return `<a-tag color="${colorMap[text] || 'default'}">${labelMap[text] || text}</a-tag>`;
    },
  },
  {
    title: '分组',
    dataIndex: 'groupName',
    key: 'groupName',
    width: 100,
  },
];

// 运行所有测试
const runAllTests = async () => {
  testResults.value = [];
  
  // 测试1: 配置加载
  await testConfigLoading();
  
  // 测试2: 字段分类
  testFieldClassification();
  
  // 测试3: 权限检查
  testPermissions();
  
  // 测试4: 配置验证
  testConfigValidation();
  
  // 测试5: 字段元数据
  testFieldMetadata();
  
  message.success(`测试完成，共运行 ${testResults.value.length} 个测试`);
};

// 测试配置加载
const testConfigLoading = async () => {
  try {
    const result = await loadFieldConfig();
    
    if (result.success) {
      testResults.value.push({
        name: '配置加载测试',
        status: 'success',
        description: `配置加载成功，来源: ${result.source}`,
        details: `字段数量: ${result.config.length}`,
      });
    } else {
      testResults.value.push({
        name: '配置加载测试',
        status: 'error',
        description: '配置加载失败',
        details: result.errors?.join('; ') || '未知错误',
      });
    }
  } catch (error: any) {
    testResults.value.push({
      name: '配置加载测试',
      status: 'error',
      description: '配置加载异常',
      details: error.message,
    });
  }
};

// 测试字段分类
const testFieldClassification = () => {
  try {
    const totalFields = fieldConfig.value.length;
    const outsideCount = outsideFields.value.length;
    const collapseCount = collapseFields.value.length;
    const hiddenCount = hiddenFields.value.length;
    
    const isValid = (outsideCount + collapseCount + hiddenCount) === totalFields;
    
    testResults.value.push({
      name: '字段分类测试',
      status: isValid ? 'success' : 'error',
      description: isValid ? '字段分类正确' : '字段分类有误',
      details: `总字段: ${totalFields}, 外部: ${outsideCount}, 折叠: ${collapseCount}, 隐藏: ${hiddenCount}`,
    });
  } catch (error: any) {
    testResults.value.push({
      name: '字段分类测试',
      status: 'error',
      description: '字段分类测试异常',
      details: error.message,
    });
  }
};

// 测试权限检查
const testPermissions = () => {
  try {
    const permissions = [
      { name: '管理员权限', value: isAdmin.value },
      { name: '配置字段权限', value: canConfigureFields.value },
      { name: '查看配置权限', value: canViewFieldConfig.value },
      { name: '编辑配置权限', value: canEditFieldConfig.value },
    ];
    
    const hasAnyPermission = permissions.some(p => p.value);
    
    testResults.value.push({
      name: '权限检查测试',
      status: hasAnyPermission ? 'success' : 'error',
      description: hasAnyPermission ? '权限检查正常' : '没有任何权限',
      details: permissions.map(p => `${p.name}: ${p.value ? '有' : '无'}`).join(', '),
    });
  } catch (error: any) {
    testResults.value.push({
      name: '权限检查测试',
      status: 'error',
      description: '权限检查测试异常',
      details: error.message,
    });
  }
};

// 测试配置验证
const testConfigValidation = () => {
  try {
    const validation = customerRegFieldManager.validateFieldConfig(fieldConfig.value);
    
    testResults.value.push({
      name: '配置验证测试',
      status: validation.isValid ? 'success' : 'error',
      description: validation.isValid ? '配置验证通过' : '配置验证失败',
      details: validation.errors.length > 0 ? validation.errors.join('; ') : '配置格式正确',
    });
  } catch (error: any) {
    testResults.value.push({
      name: '配置验证测试',
      status: 'error',
      description: '配置验证测试异常',
      details: error.message,
    });
  }
};

// 测试字段元数据
const testFieldMetadata = () => {
  try {
    const metadata = customerRegFieldManager.getAllFieldMetadata();
    const metadataCount = Object.keys(metadata).length;
    const configCount = fieldConfig.value.length;
    
    // 检查是否所有配置的字段都有对应的元数据
    const missingMetadata = fieldConfig.value.filter(field => !metadata[field.fieldKey]);
    
    testResults.value.push({
      name: '字段元数据测试',
      status: missingMetadata.length === 0 ? 'success' : 'error',
      description: missingMetadata.length === 0 ? '字段元数据完整' : '存在缺失的字段元数据',
      details: `元数据数量: ${metadataCount}, 配置字段数量: ${configCount}, 缺失元数据: ${missingMetadata.length}`,
    });
  } catch (error: any) {
    testResults.value.push({
      name: '字段元数据测试',
      status: 'error',
      description: '字段元数据测试异常',
      details: error.message,
    });
  }
};

// 清除测试结果
const clearTestResults = () => {
  testResults.value = [];
  message.success('测试结果已清除');
};

// 处理表单提交
const handleFormSubmit = (data: Record<string, any>) => {
  console.log('表单提交数据:', data);
  message.success('表单提交成功');
};

// 处理表单重置
const handleFormReset = () => {
  console.log('表单已重置');
  message.info('表单已重置');
};

// 组件挂载时自动运行测试
onMounted(async () => {
  // 等待配置加载完成后运行测试
  setTimeout(() => {
    runAllTests();
  }, 1000);
});
</script>

<style scoped>
.field-config-test {
  padding: 16px;
}

.test-controls {
  margin-bottom: 16px;
}

.test-results {
  margin-top: 16px;
}

.test-success {
  color: #52c41a;
}

.test-error {
  color: #ff4d4f;
}

.test-details {
  margin-top: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
}

.test-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.permission-test,
.config-status,
.field-config-details {
  margin-top: 16px;
}
</style>
