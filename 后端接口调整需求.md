# 后端接口调整需求

## 📋 概述

基于字段折叠控制系统的重构，需要对后端接口进行相应的调整以支持新功能。

## 🔄 需要调整的接口

### 1. **字段配置数据结构升级**

#### 当前数据结构
```json
{
  "fieldKey": "nation",
  "fieldName": "民族",
  "isVisible": false,
  "groupName": "基础信息"
}
```

#### 新的数据结构（向后兼容）
```json
{
  "fieldKey": "nation",
  "fieldName": "民族",
  "displayLocation": "outside",  // 新增：outside/collapse/hidden
  "groupName": "basicInfo",      // 标准化分组名
  "sortOrder": 1,               // 新增：排序号
  "isVisible": false            // 保留：向后兼容
}
```

### 2. **现有接口升级**

#### 2.1 获取当前生效配置接口
**接口：** `GET /reg/formFieldConfig/active`

**当前参数：**
```json
{
  "formType": "customer_reg"
}
```

**建议升级参数：**
```json
{
  "formType": "customer_reg",
  "centerId": "center_001",     // 新增：支持多中心
  "version": "v2"               // 新增：API版本控制
}
```

**响应数据升级：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "config_001",
    "configName": "默认配置",
    "centerId": "center_001",
    "centerName": "第一体检中心",
    "formType": "customer_reg",
    "isActive": true,
    "version": "1.0.0",          // 新增：配置版本
    "fields": [
      {
        "fieldKey": "nation",
        "fieldName": "民族",
        "displayLocation": "outside",
        "groupName": "basicInfo",
        "sortOrder": 1,
        "isVisible": false
      }
    ],
    "createTime": "2024-01-01T00:00:00Z",
    "updateTime": "2024-01-01T00:00:00Z",
    "createBy": "admin",
    "updateBy": "admin"
  }
}
```

#### 2.2 保存配置接口
**接口：** `POST /reg/formFieldConfig/save`

**请求体升级：**
```json
{
  "id": "config_001",           // 可选：更新时提供
  "configName": "自定义配置",
  "centerId": "center_001",
  "centerName": "第一体检中心",
  "formType": "customer_reg",
  "isActive": true,
  "fields": [
    {
      "fieldKey": "nation",
      "fieldName": "民族",
      "displayLocation": "outside",
      "groupName": "basicInfo",
      "sortOrder": 1
    }
  ]
}
```

### 3. **新增接口需求**

#### 3.1 权限检查接口
**接口：** `GET /system/permission/check`

**参数：**
```json
{
  "permissions": ["field:config:edit", "field:config:view"],
  "userId": "user_001"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "field:config:edit": true,
    "field:config:view": true
  }
}
```

#### 3.2 用户权限信息接口
**接口：** `GET /system/user/permissions`

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "user_001",
    "username": "admin",
    "roles": ["center_admin"],
    "permissions": [
      "field:config:view",
      "field:config:edit",
      "form:config:manage"
    ],
    "centerId": "center_001",
    "centerName": "第一体检中心"
  }
}
```

#### 3.3 配置版本管理接口
**接口：** `GET /reg/formFieldConfig/versions`

**参数：**
```json
{
  "formType": "customer_reg",
  "centerId": "center_001"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "config_001",
      "configName": "默认配置",
      "version": "1.0.0",
      "isActive": true,
      "createTime": "2024-01-01T00:00:00Z",
      "createBy": "admin"
    }
  ]
}
```

#### 3.4 配置验证接口
**接口：** `POST /reg/formFieldConfig/validate`

**请求体：**
```json
{
  "formType": "customer_reg",
  "fields": [
    {
      "fieldKey": "nation",
      "fieldName": "民族",
      "displayLocation": "outside",
      "groupName": "basicInfo"
    }
  ]
}
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isValid": true,
    "errors": [],
    "warnings": ["建议设置排序号"]
  }
}
```

## 🔧 数据库表结构调整

### 1. **字段配置表升级**

```sql
-- 现有表结构
CREATE TABLE form_field_config (
  id VARCHAR(32) PRIMARY KEY,
  config_name VARCHAR(100),
  center_id VARCHAR(32),
  center_name VARCHAR(100),
  form_type VARCHAR(50),
  is_active TINYINT(1),
  fields JSON,
  create_time DATETIME,
  update_time DATETIME,
  create_by VARCHAR(32),
  update_by VARCHAR(32)
);

-- 建议添加的字段
ALTER TABLE form_field_config 
ADD COLUMN version VARCHAR(20) DEFAULT '1.0.0' COMMENT '配置版本',
ADD COLUMN description TEXT COMMENT '配置描述',
ADD COLUMN tags VARCHAR(200) COMMENT '标签，逗号分隔',
ADD INDEX idx_center_form_type (center_id, form_type),
ADD INDEX idx_active_version (is_active, version);
```

### 2. **字段元数据表（新增）**

```sql
-- 字段元数据表
CREATE TABLE form_field_metadata (
  id VARCHAR(32) PRIMARY KEY,
  field_key VARCHAR(50) NOT NULL COMMENT '字段键',
  field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
  field_type VARCHAR(20) NOT NULL COMMENT '字段类型',
  group_name VARCHAR(50) COMMENT '分组名称',
  default_location VARCHAR(20) DEFAULT 'outside' COMMENT '默认显示位置',
  form_type VARCHAR(50) NOT NULL COMMENT '表单类型',
  is_required TINYINT(1) DEFAULT 0 COMMENT '是否必填',
  placeholder VARCHAR(200) COMMENT '占位符',
  dict_code VARCHAR(50) COMMENT '字典编码',
  validation_rules JSON COMMENT '验证规则',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_field_form (field_key, form_type),
  INDEX idx_form_type (form_type),
  INDEX idx_group (group_name)
) COMMENT='表单字段元数据表';
```

## 🔄 迁移策略

### 1. **向后兼容性**

- 保留 `isVisible` 字段，同时支持新的 `displayLocation` 字段
- 提供数据迁移脚本，将旧格式转换为新格式
- API 版本控制，支持 v1 和 v2 接口

### 2. **数据迁移脚本**

```sql
-- 迁移现有配置数据
UPDATE form_field_config 
SET fields = JSON_SET(
  fields,
  '$[*].displayLocation',
  CASE 
    WHEN JSON_EXTRACT(fields, '$[*].isVisible') = true THEN 'collapse'
    ELSE 'outside'
  END
)
WHERE JSON_VALID(fields);
```

### 3. **渐进式升级**

1. **第一阶段**：后端支持新字段，前端保持兼容
2. **第二阶段**：前端开始使用新字段，后端同时支持新旧格式
3. **第三阶段**：完全切换到新格式，移除旧字段支持

## 📝 接口文档更新

### 1. **API 文档**

需要更新 Swagger/OpenAPI 文档，包括：
- 新的数据结构定义
- 权限相关接口
- 版本控制说明
- 错误码定义

### 2. **错误码定义**

```json
{
  "FIELD_CONFIG_001": "字段配置不存在",
  "FIELD_CONFIG_002": "字段配置验证失败",
  "FIELD_CONFIG_003": "权限不足",
  "FIELD_CONFIG_004": "配置版本冲突",
  "FIELD_CONFIG_005": "字段元数据不存在"
}
```

## 🧪 测试建议

### 1. **接口测试**

- 新旧数据格式兼容性测试
- 权限控制测试
- 配置验证测试
- 并发访问测试

### 2. **数据迁移测试**

- 大量数据迁移性能测试
- 数据完整性验证
- 回滚机制测试

## 🚀 实施优先级

### 高优先级（必须）
1. 字段配置数据结构升级
2. 向后兼容性支持
3. 基本权限检查接口

### 中优先级（建议）
1. 配置版本管理
2. 字段元数据管理
3. 配置验证接口

### 低优先级（可选）
1. 高级权限控制
2. 配置模板功能
3. 批量操作接口

## 📞 协调建议

1. **与后端团队协调**：确认接口实现时间和优先级
2. **数据库变更**：协调 DBA 进行表结构升级
3. **测试环境**：准备测试数据和测试用例
4. **上线计划**：制定分阶段上线计划，确保平滑过渡
