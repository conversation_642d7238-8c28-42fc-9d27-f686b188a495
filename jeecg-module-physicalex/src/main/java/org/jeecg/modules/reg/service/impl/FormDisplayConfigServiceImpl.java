package org.jeecg.modules.reg.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.entity.FieldDisplayConfig;
import org.jeecg.modules.reg.entity.FormDisplayConfig;
import org.jeecg.modules.reg.mapper.FieldDisplayConfigMapper;
import org.jeecg.modules.reg.mapper.FormDisplayConfigMapper;
import org.jeecg.modules.reg.service.IFormDisplayConfigService;
import org.jeecg.modules.reg.vo.FormDisplayConfigQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 表单显示配置Service实现
 */
@Slf4j
@Service
@Transactional
public class FormDisplayConfigServiceImpl extends ServiceImpl<FormDisplayConfigMapper, FormDisplayConfig>
        implements IFormDisplayConfigService {

    @Autowired
    private FieldDisplayConfigMapper fieldMapper;

    @Override
    public IPage<FormDisplayConfig> getConfigList(Page<FormDisplayConfig> page, FormDisplayConfigQueryParam param) {
        QueryWrapper<FormDisplayConfig> queryWrapper = new QueryWrapper<>();

        if (StrUtil.isNotBlank(param.getConfigName())) {
            queryWrapper.like("config_name", param.getConfigName());
        }
        if (StrUtil.isNotBlank(param.getFormType())) {
            queryWrapper.eq("form_type", param.getFormType());
        }
        if (param.getIsActive() != null) {
            queryWrapper.eq("is_active", param.getIsActive());
        }
        if (StrUtil.isNotBlank(param.getCenterId())) {
            queryWrapper.eq("center_id", param.getCenterId());
        }

        queryWrapper.orderByDesc("create_time");
        return this.page(page, queryWrapper);
    }

    @Override
    public FormDisplayConfig getConfigWithFields(String id) {
        FormDisplayConfig config = this.getById(id);
        if (config != null) {
            List<FieldDisplayConfig> fields = fieldMapper.selectList(
                    new QueryWrapper<FieldDisplayConfig>().eq("config_id", id)
            );
            config.setFields(fields);
        }
        return config;
    }

    @Override
    public boolean saveConfigWithFields(FormDisplayConfig config) {
        try {
            // 设置默认值
            if (StrUtil.isBlank(config.getCenterId())) {
                config.setCenterId("default_center");
                config.setCenterName("默认体检中心");
            }

            // 先查询是否已存在
            QueryWrapper<FormDisplayConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("center_id", config.getCenterId())
                    .eq("form_type", config.getFormType());
            FormDisplayConfig existingConfig = this.getOne(queryWrapper);

            if (existingConfig != null) {
                // 更新现有配置
                config.setId(existingConfig.getId());
                // 删除原有字段配置
                fieldMapper.deleteByConfigId(config.getId());
            }
            // 使用 saveOrUpdate 方法
            this.saveOrUpdate(config);

            // 保存字段配置
            if (CollUtil.isNotEmpty(config.getFields())) {
                for (FieldDisplayConfig field : config.getFields()) {
                    field.setConfigId(config.getId());
                    fieldMapper.insert(field);
                }
                //fieldMapper.batchInsert(config.getFields());
            }

            return true;
        } catch (Exception e) {
            log.error("保存配置失败", e);
            throw new RuntimeException("保存配置失败: " + e.getMessage());
        }
    }

    @Override
    public FormDisplayConfig getActiveConfig(String formType) {
        return baseMapper.getActiveConfigWithFields(formType);
    }

    @Override
    public FormDisplayConfig getDefaultConfig(String formType) {
        // 返回默认配置模板
        FormDisplayConfig defaultConfig = new FormDisplayConfig();
        defaultConfig.setConfigName("默认配置");
        defaultConfig.setFormType(formType);
        defaultConfig.setIsActive(true);

        // 根据表单类型返回不同的默认字段配置
        List<FieldDisplayConfig> defaultFields = getDefaultFieldsByFormType(formType);
        defaultConfig.setFields(defaultFields);

        return defaultConfig;
    }

    private List<FieldDisplayConfig> getDefaultFieldsByFormType(String formType) {
        List<FieldDisplayConfig> fields = new ArrayList<>();

        if ("customer_reg".equals(formType)) {
            // 客户登记表单默认字段
            fields.add(createField("nation", "民族", true, "基础信息"));
            fields.add(createField("bloodType", "血型", true, "基础信息"));
            fields.add(createField("countryCode", "国籍", true, "基础信息"));
            fields.add(createField("postCode", "邮政编码", false, "基础信息"));
            fields.add(createField("eduLevel", "文化程度", true, "基础信息"));
            fields.add(createField("marriageStatus", "婚姻状况", false, "基础信息"));
            fields.add(createField("customerCategory", "客户类别", false, "基础信息"));
            fields.add(createField("pcaCode", "省市区县", false, "地址信息"));
            fields.add(createField("address", "详细地址", false, "地址信息"));
            fields.add(createField("emergencyContact", "紧急联系人", false, "联系信息"));
            fields.add(createField("emergencyPhone", "紧急电话", false, "联系信息"));
            fields.add(createField("isPregnancyPrep", "是否备孕", false, "健康信息"));
            fields.add(createField("healthNo", "健康证号", false, "证件信息"));
            fields.add(createField("examCardNo", "体检卡号", false, "证件信息"));
            fields.add(createField("workYears", "工龄", false, "职业信息"));
            fields.add(createField("companyName", "单位名称", false, "单位信息"));
            fields.add(createField("belongCompany", "所属单位", false, "单位信息"));
            fields.add(createField("department", "所属科室", false, "单位信息"));
            fields.add(createField("supplyFlag", "补检", true, "标识信息"));
            fields.add(createField("prePayFlag", "预缴", true, "标识信息"));
            fields.add(createField("reExamStatus", "是否复查", true, "标识信息"));
            fields.add(createField("reExamRemark", "复查备注", true, "标识信息"));
            fields.add(createField("recipeTitle", "发票抬头", false, "财务信息"));
            fields.add(createField("originalIdCard", "原检证件号", false, "原检信息"));
            fields.add(createField("relationWithOriginal", "与原检关系", false, "原检信息"));
            fields.add(createField("introducer", "介绍人", false, "其他信息"));
            fields.add(createField("secretLevel", "保密等级", false, "其他信息"));
            fields.add(createField("remark", "备注", false, "其他信息"));
        }

        return fields;
    }

    private FieldDisplayConfig createField(String fieldKey, String fieldName, boolean isVisible, String groupName) {
        FieldDisplayConfig field = new FieldDisplayConfig();
        field.setFieldKey(fieldKey);
        field.setFieldName(fieldName);
        field.setIsVisible(isVisible);
        field.setGroupName(groupName);
        return field;
    }
}
