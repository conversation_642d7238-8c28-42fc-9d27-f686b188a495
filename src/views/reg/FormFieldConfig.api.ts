import { defHttp } from '/@/utils/http/axios';
import {
  FieldDisplayConfig,
  FormDisplayConfig,
  FieldKeys,
  FieldDisplayLocation,
  FieldGroups,
} from './config/FieldDefinitions';
import { FormTypes, customerRegFieldManager } from './config/FieldConfigUtils';

enum Api {
  // 获取表单字段配置
  getFormFieldConfig = '/reg/formFieldConfig/list',
  // 保存表单字段配置
  saveFormFieldConfig = '/reg/formFieldConfig/save',
  // 删除表单字段配置
  deleteFormFieldConfig = '/reg/formFieldConfig/delete',
  // 获取当前生效的配置
  getActiveConfig = '/reg/formFieldConfig/active',
  // 获取默认配置
  getDefaultConfig = '/reg/formFieldConfig/default',
  // 复制配置
  copyConfig = '/reg/formFieldConfig/copy',
}

// 接口和类型定义已移至 ./config/FieldDefinitions.ts

/**
 * 获取表单显示配置列表
 */
export const getFormDisplayConfigList = (params: { centerId?: string; formType?: string; pageNo?: number; pageSize?: number }) => {
  return defHttp.get<{
    records: FormDisplayConfig[];
    total: number;
  }>({
    url: Api.getFormFieldConfig,
    params,
  });
};

/**
 * 获取当前生效的显示配置
 */
export const getActiveFormDisplayConfig = (params: { formType: string }) => {
  return defHttp.get<FormDisplayConfig>({
    url: Api.getActiveConfig,
    params,
  });
};

/**
 * 保存表单显示配置（保存或更新）
 */
export const saveFormDisplayConfig = (data: FormDisplayConfig) => {
  return defHttp.post({
    url: Api.saveFormFieldConfig,
    data,
  });
};

/**
 * 保存或更新表单显示配置
 */
export const saveOrUpdateFormDisplayConfig = (data: FormDisplayConfig) => {
  return defHttp.post({
    url: Api.saveFormFieldConfig + '/saveOrUpdate',
    data,
  });
};

/**
 * 删除表单显示配置
 */
export const deleteFormDisplayConfig = (id: string) => {
  return defHttp.delete({
    url: Api.deleteFormFieldConfig + '/' + id,
  });
};

/**
 * 预定义的表单类型 (已废弃，请使用 FormTypes 枚举)
 * @deprecated 请使用 ./config/FieldConfigUtils 中的 FormTypes 枚举
 */
export const FORM_TYPES = {
  CUSTOMER_REG: FormTypes.CUSTOMER_REG,
  COMPANY_REG: FormTypes.COMPANY_REG,
  EXAM_CONFIG: FormTypes.EXAM_CONFIG,
} as const;

/**
 * 客户登记表单可配置字段列表 (已废弃)
 * @deprecated 请使用 customerRegFieldManager.createDefaultFieldDisplayConfig() 方法
 */
export const CUSTOMER_REG_CONFIGURABLE_FIELDS: FieldDisplayConfig[] = customerRegFieldManager.createDefaultFieldDisplayConfig();

/**
 * 工具函数：获取默认字段配置
 */
export function getDefaultFieldConfig(formType: FormTypes = FormTypes.CUSTOMER_REG): FieldDisplayConfig[] {
  return customerRegFieldManager.createDefaultFieldDisplayConfig();
}

/**
 * 工具函数：创建默认表单配置
 */
export function createDefaultFormConfig(
  configName: string,
  centerId: string,
  centerName: string,
  formType: FormTypes = FormTypes.CUSTOMER_REG
): FormDisplayConfig {
  return customerRegFieldManager.createDefaultFormDisplayConfig(configName, centerId, centerName);
}

/**
 * 工具函数：验证字段配置
 */
export function validateFieldConfig(config: FieldDisplayConfig[]): { isValid: boolean; errors: string[] } {
  return customerRegFieldManager.validateFieldConfig(config);
}

/**
 * 工具函数：迁移旧版本配置
 */
export function migrateOldFieldConfig(oldConfig: any[]): FieldDisplayConfig[] {
  return customerRegFieldManager.migrateOldConfig(oldConfig);
}
