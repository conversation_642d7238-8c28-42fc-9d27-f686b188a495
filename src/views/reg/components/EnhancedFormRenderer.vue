<template>
  <div class="enhanced-form-renderer">
    <!-- 外部字段区域 -->
    <a-row :gutter="16" v-if="outsideFields.length > 0">
      <DynamicFormField
        v-for="field in outsideFields"
        :key="field.fieldKey"
        :fieldMetadata="getFieldMetadata(field.fieldKey)"
        :modelValue="modelValue"
        :disabled="disabled"
        :validateInfos="validateInfos"
        :displayLocation="FieldDisplayLocation.OUTSIDE"
        :currentLocation="FieldDisplayLocation.OUTSIDE"
        :selectOptions="getSelectOptions(field.fieldKey)"
        :searchExtra="getSearchExtra(field.fieldKey)"
        :searchLoading="getSearchLoading(field.fieldKey)"
        @update:modelValue="handleModelUpdate"
        @change="handleFieldChange"
        @search="handleFieldSearch"
      />
    </a-row>

    <!-- 折叠面板区域 -->
    <div v-if="collapseFields.length > 0" class="collapse-section">
      <div class="collapse-header">
        <a-button 
          type="dashed" 
          size="small" 
          danger 
          @click="toggleCollapse"
          class="collapse-toggle-btn"
        >
          {{ isCollapsed ? '展开全部' : '收起' }}
          <Icon :icon="isCollapsed ? 'ant-design:down-outlined' : 'ant-design:up-outlined'" />
        </a-button>
        
        <!-- 字段配置按钮 -->
        <a-button 
          v-if="showConfigButton && hasConfigPermission" 
          type="dashed" 
          size="small" 
          @click="openFieldConfig"
          class="config-btn"
        >
          <Icon icon="ant-design:setting-outlined" />
          配置字段
        </a-button>
      </div>

      <!-- 折叠面板内容 -->
      <div v-show="!isCollapsed" class="collapse-content">
        <a-row :gutter="16">
          <DynamicFormField
            v-for="field in collapseFields"
            :key="field.fieldKey"
            :fieldMetadata="getFieldMetadata(field.fieldKey)"
            :modelValue="modelValue"
            :disabled="disabled"
            :validateInfos="validateInfos"
            :displayLocation="FieldDisplayLocation.COLLAPSE"
            :currentLocation="FieldDisplayLocation.COLLAPSE"
            :selectOptions="getSelectOptions(field.fieldKey)"
            :searchExtra="getSearchExtra(field.fieldKey)"
            :searchLoading="getSearchLoading(field.fieldKey)"
            @update:modelValue="handleModelUpdate"
            @change="handleFieldChange"
            @search="handleFieldSearch"
          />
        </a-row>
      </div>
    </div>

    <!-- 字段配置弹窗 -->
    <FormFieldConfigModal 
      ref="configModalRef" 
      @success="handleConfigSuccess" 
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue';
import { Icon } from '/@/components/Icon';
import DynamicFormField from './DynamicFormField.vue';
import FormFieldConfigModal from './FormFieldConfigModal.vue';
import {
  FieldKeys,
  FieldDisplayLocation,
  FieldDisplayConfig,
  FieldMetadata,
} from '../config/FieldDefinitions';
import { 
  FormTypes, 
  FieldConfigManager, 
  createFieldConfigManager 
} from '../config/FieldConfigUtils';

interface Props {
  modelValue: Record<string, any>;
  fieldConfig: FieldDisplayConfig[];
  formType?: FormTypes;
  disabled?: boolean;
  validateInfos?: Record<string, any>;
  showConfigButton?: boolean;
  hasConfigPermission?: boolean;
  selectOptionsMap?: Record<FieldKeys, Array<{ label: string; value: any }>>;
  searchExtrasMap?: Record<FieldKeys, string>;
  searchLoadingMap?: Record<FieldKeys, boolean>;
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void;
  (e: 'fieldChange', fieldKey: FieldKeys, value: any, option?: any): void;
  (e: 'fieldSearch', fieldKey: FieldKeys, value: string): void;
  (e: 'configSuccess'): void;
  (e: 'openConfig'): void;
}

const props = withDefaults(defineProps<Props>(), {
  formType: FormTypes.CUSTOMER_REG,
  disabled: false,
  showConfigButton: true,
  hasConfigPermission: false,
  selectOptionsMap: () => ({}),
  searchExtrasMap: () => ({}),
  searchLoadingMap: () => ({}),
});

const emit = defineEmits<Emits>();

// 状态管理
const isCollapsed = ref(true);
const configModalRef = ref();

// 字段配置管理器
const fieldManager = computed(() => createFieldConfigManager(props.formType));

// 计算外部显示的字段
const outsideFields = computed(() => {
  return fieldManager.value.getOutsideFields(props.fieldConfig);
});

// 计算折叠面板中显示的字段
const collapseFields = computed(() => {
  return fieldManager.value.getCollapseFields(props.fieldConfig);
});

// 获取字段元数据
const getFieldMetadata = (fieldKey: FieldKeys): FieldMetadata => {
  const metadata = fieldManager.value.getFieldMetadata(fieldKey);
  if (!metadata) {
    console.warn(`字段 ${fieldKey} 的元数据未找到`);
    return {
      key: fieldKey,
      name: fieldKey,
      type: 'input' as any,
      group: 'otherInfo' as any,
      defaultLocation: FieldDisplayLocation.OUTSIDE,
      span: 12,
      placeholder: `请输入${fieldKey}`,
    };
  }
  return metadata;
};

// 获取选择框选项
const getSelectOptions = (fieldKey: FieldKeys) => {
  return props.selectOptionsMap[fieldKey] || [];
};

// 获取搜索额外信息
const getSearchExtra = (fieldKey: FieldKeys) => {
  return props.searchExtrasMap[fieldKey] || '';
};

// 获取搜索加载状态
const getSearchLoading = (fieldKey: FieldKeys) => {
  return props.searchLoadingMap[fieldKey] || false;
};

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 处理模型更新
const handleModelUpdate = (value: Record<string, any>) => {
  emit('update:modelValue', value);
};

// 处理字段变化
const handleFieldChange = (fieldKey: FieldKeys, value: any, option?: any) => {
  emit('fieldChange', fieldKey, value, option);
};

// 处理字段搜索
const handleFieldSearch = (fieldKey: FieldKeys, value: string) => {
  emit('fieldSearch', fieldKey, value);
};

// 打开字段配置
const openFieldConfig = () => {
  emit('openConfig');
  configModalRef.value?.open();
};

// 配置成功回调
const handleConfigSuccess = () => {
  emit('configSuccess');
};

// 检查字段是否在指定位置显示
const isFieldInLocation = (fieldKey: FieldKeys, location: FieldDisplayLocation): boolean => {
  return fieldManager.value.isFieldInLocation(fieldKey, location, props.fieldConfig);
};

// 暴露方法给父组件
defineExpose({
  isFieldInLocation,
  toggleCollapse,
  openFieldConfig,
});
</script>

<style scoped>
.enhanced-form-renderer {
  width: 100%;
}

.collapse-section {
  margin-top: 16px;
}

.collapse-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.collapse-toggle-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.config-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.collapse-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}
</style>
