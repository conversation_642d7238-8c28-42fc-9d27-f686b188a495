package org.jeecg.modules.reg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;
import org.jeecg.modules.reg.entity.FormDisplayConfig;
import org.jeecg.modules.reg.entity.FieldDisplayConfig;

import java.util.List;

/**
 * 表单显示配置Mapper
 */
@Mapper
public interface FormDisplayConfigMapper extends BaseMapper<FormDisplayConfig> {

    /**
     * 获取配置详情（包含字段列表）
     */
    @Select("SELECT * FROM form_display_config WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "fields", column = "id",
                    javaType = List.class,
                    many = @Many(select = "org.jeecg.modules.reg.mapper.FieldDisplayConfigMapper.selectByConfigId"))
    })
    FormDisplayConfig getConfigWithFields(@Param("id") String id);

    /**
     * 获取当前生效的配置
     */
    @Select("SELECT * FROM form_display_config WHERE center_id = #{centerId} AND form_type = #{formType} AND is_active = 1 LIMIT 1")
    FormDisplayConfig getActiveConfig(@Param("centerId") String centerId, @Param("formType") String formType);

    /**
     * 获取当前生效的配置（包含字段）
     */
    @Select("SELECT * FROM form_display_config WHERE form_type = #{formType} AND is_active = 1 LIMIT 1")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "fields", column = "id",
                    javaType = List.class,
                    many = @Many(select = "org.jeecg.modules.reg.mapper.FieldDisplayConfigMapper.selectByConfigId"))
    })
    FormDisplayConfig getActiveConfigWithFields(@Param("formType") String formType);
}