/**
 * 权限检查组合式函数
 * 提供Vue组件中使用的权限检查功能
 */

import { computed, ref, onMounted } from 'vue';
import { 
  permissionManager, 
  PermissionTypes, 
  UserRoles, 
  UserInfo,
  initPermissionManager 
} from '../config/PermissionManager';

/**
 * 权限检查组合式函数
 */
export function usePermissions() {
  // 响应式状态
  const isLoading = ref(true);
  const userInfo = ref<UserInfo | null>(null);

  // 初始化权限管理器
  const initPermissions = async () => {
    try {
      isLoading.value = true;
      await initPermissionManager();
      userInfo.value = permissionManager.getUserInfo();
    } catch (error) {
      console.error('初始化权限失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // 计算属性：权限检查
  const hasPermission = (permission: PermissionTypes) => {
    return computed(() => permissionManager.hasPermission(permission));
  };

  const hasAnyPermission = (permissions: PermissionTypes[]) => {
    return computed(() => permissionManager.hasAnyPermission(permissions));
  };

  const hasAllPermissions = (permissions: PermissionTypes[]) => {
    return computed(() => permissionManager.hasAllPermissions(permissions));
  };

  const hasRole = (role: UserRoles) => {
    return computed(() => permissionManager.hasRole(role));
  };

  const hasAnyRole = (roles: UserRoles[]) => {
    return computed(() => permissionManager.hasAnyRole(roles));
  };

  // 计算属性：常用权限检查
  const isAdmin = computed(() => permissionManager.isAdmin());
  const isSuperAdmin = computed(() => permissionManager.isSuperAdmin());
  const isCenterAdmin = computed(() => permissionManager.isCenterAdmin());

  // 计算属性：字段配置权限
  const canConfigureFields = computed(() => permissionManager.canConfigureFields());
  const canViewFieldConfig = computed(() => permissionManager.canViewFieldConfig());
  const canDeleteFieldConfig = computed(() => permissionManager.canDeleteFieldConfig());
  const canCreateFieldConfig = computed(() => permissionManager.canCreateFieldConfig());
  const canEditFieldConfig = computed(() => permissionManager.canEditFieldConfig());
  const canManageFormConfig = computed(() => permissionManager.canManageFormConfig());
  const canApplyFormConfig = computed(() => permissionManager.canApplyFormConfig());

  // 计算属性：用户信息
  const currentUser = computed(() => userInfo.value);
  const userPermissions = computed(() => permissionManager.getUserPermissions());

  // 权限检查函数（非响应式）
  const checkPermission = (permission: PermissionTypes): boolean => {
    return permissionManager.hasPermission(permission);
  };

  const checkAnyPermission = (permissions: PermissionTypes[]): boolean => {
    return permissionManager.hasAnyPermission(permissions);
  };

  const checkAllPermissions = (permissions: PermissionTypes[]): boolean => {
    return permissionManager.hasAllPermissions(permissions);
  };

  const checkRole = (role: UserRoles): boolean => {
    return permissionManager.hasRole(role);
  };

  const checkAnyRole = (roles: UserRoles[]): boolean => {
    return permissionManager.hasAnyRole(roles);
  };

  // 权限验证函数（抛出异常）
  const requirePermission = (permission: PermissionTypes, message?: string) => {
    if (!permissionManager.hasPermission(permission)) {
      const errorMessage = message || `权限不足，需要权限: ${permissionManager.getPermissionDescription(permission)}`;
      throw new Error(errorMessage);
    }
  };

  const requireRole = (role: UserRoles, message?: string) => {
    if (!permissionManager.hasRole(role)) {
      const errorMessage = message || `角色不足，需要角色: ${role}`;
      throw new Error(errorMessage);
    }
  };

  // 权限提示函数
  const getPermissionMessage = (permission: PermissionTypes): string => {
    return permissionManager.getPermissionDescription(permission);
  };

  // 更新用户信息
  const updateUserInfo = (newUserInfo: UserInfo) => {
    permissionManager.setUserInfo(newUserInfo);
    userInfo.value = newUserInfo;
  };

  // 组件挂载时初始化
  onMounted(() => {
    initPermissions();
  });

  return {
    // 状态
    isLoading,
    userInfo: currentUser,
    userPermissions,

    // 初始化
    initPermissions,
    updateUserInfo,

    // 响应式权限检查
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,

    // 响应式常用权限
    isAdmin,
    isSuperAdmin,
    isCenterAdmin,
    canConfigureFields,
    canViewFieldConfig,
    canDeleteFieldConfig,
    canCreateFieldConfig,
    canEditFieldConfig,
    canManageFormConfig,
    canApplyFormConfig,

    // 非响应式权限检查
    checkPermission,
    checkAnyPermission,
    checkAllPermissions,
    checkRole,
    checkAnyRole,

    // 权限验证
    requirePermission,
    requireRole,

    // 工具函数
    getPermissionMessage,
  };
}

/**
 * 字段配置权限检查组合式函数
 * 专门用于字段配置相关的权限检查
 */
export function useFieldConfigPermissions() {
  const permissions = usePermissions();

  // 字段配置相关的权限检查
  const canShowConfigButton = computed(() => {
    return permissions.canConfigureFields.value || permissions.canManageFormConfig.value;
  });

  const canModifyFieldConfig = computed(() => {
    return permissions.canEditFieldConfig.value || permissions.canCreateFieldConfig.value;
  });

  const canDeleteConfig = computed(() => {
    return permissions.canDeleteFieldConfig.value;
  });

  const canCreateConfig = computed(() => {
    return permissions.canCreateFieldConfig.value;
  });

  const canViewConfig = computed(() => {
    return permissions.canViewFieldConfig.value;
  });

  // 检查是否可以操作指定中心的配置
  const canOperateCenterConfig = (centerId?: string) => {
    return computed(() => {
      // 超级管理员可以操作所有中心的配置
      if (permissions.isSuperAdmin.value) {
        return true;
      }

      // 中心管理员只能操作自己中心的配置
      if (permissions.isCenterAdmin.value) {
        const currentUser = permissions.userInfo.value;
        return !centerId || !currentUser?.centerId || currentUser.centerId === centerId;
      }

      // 其他角色根据具体权限判断
      return permissions.canConfigureFields.value;
    });
  };

  return {
    ...permissions,
    
    // 字段配置专用权限
    canShowConfigButton,
    canModifyFieldConfig,
    canDeleteConfig,
    canCreateConfig,
    canViewConfig,
    canOperateCenterConfig,
  };
}

/**
 * 权限指令
 * 用于在模板中进行权限控制
 */
export const vPermission = {
  mounted(el: HTMLElement, binding: { value: PermissionTypes | PermissionTypes[] }) {
    const { value } = binding;
    const hasPermission = Array.isArray(value) 
      ? permissionManager.hasAnyPermission(value)
      : permissionManager.hasPermission(value);

    if (!hasPermission) {
      el.style.display = 'none';
    }
  },
  updated(el: HTMLElement, binding: { value: PermissionTypes | PermissionTypes[] }) {
    const { value } = binding;
    const hasPermission = Array.isArray(value) 
      ? permissionManager.hasAnyPermission(value)
      : permissionManager.hasPermission(value);

    el.style.display = hasPermission ? '' : 'none';
  },
};

/**
 * 角色指令
 * 用于在模板中进行角色控制
 */
export const vRole = {
  mounted(el: HTMLElement, binding: { value: UserRoles | UserRoles[] }) {
    const { value } = binding;
    const hasRole = Array.isArray(value) 
      ? permissionManager.hasAnyRole(value)
      : permissionManager.hasRole(value);

    if (!hasRole) {
      el.style.display = 'none';
    }
  },
  updated(el: HTMLElement, binding: { value: UserRoles | UserRoles[] }) {
    const { value } = binding;
    const hasRole = Array.isArray(value) 
      ? permissionManager.hasAnyRole(value)
      : permissionManager.hasRole(value);

    el.style.display = hasRole ? '' : 'none';
  },
};
