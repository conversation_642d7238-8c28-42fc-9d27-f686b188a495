/**
 * 权限管理器
 * 统一管理字段配置相关的权限检查
 */

/**
 * 权限类型枚举
 */
export enum PermissionTypes {
  // 字段配置权限
  FIELD_CONFIG_VIEW = 'field:config:view',
  FIELD_CONFIG_EDIT = 'field:config:edit',
  FIELD_CONFIG_DELETE = 'field:config:delete',
  FIELD_CONFIG_CREATE = 'field:config:create',
  
  // 表单配置权限
  FORM_CONFIG_MANAGE = 'form:config:manage',
  FORM_CONFIG_APPLY = 'form:config:apply',
  
  // 系统管理权限
  SYSTEM_ADMIN = 'system:admin',
  CENTER_ADMIN = 'center:admin',
  
  // 用户角色权限
  ROLE_ADMIN = 'role:admin',
  ROLE_MANAGER = 'role:manager',
  ROLE_OPERATOR = 'role:operator',
}

/**
 * 用户角色枚举
 */
export enum UserRoles {
  SUPER_ADMIN = 'super_admin',
  CENTER_ADMIN = 'center_admin',
  DEPARTMENT_MANAGER = 'department_manager',
  OPERATOR = 'operator',
  VIEWER = 'viewer',
}

/**
 * 权限配置接口
 */
export interface PermissionConfig {
  role: UserRoles;
  permissions: PermissionTypes[];
  description: string;
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string;
  username: string;
  roles: UserRoles[];
  permissions: PermissionTypes[];
  centerId?: string;
  centerName?: string;
  departmentId?: string;
  departmentName?: string;
}

/**
 * 默认权限配置
 */
export const DEFAULT_PERMISSION_CONFIG: PermissionConfig[] = [
  {
    role: UserRoles.SUPER_ADMIN,
    permissions: [
      PermissionTypes.FIELD_CONFIG_VIEW,
      PermissionTypes.FIELD_CONFIG_EDIT,
      PermissionTypes.FIELD_CONFIG_DELETE,
      PermissionTypes.FIELD_CONFIG_CREATE,
      PermissionTypes.FORM_CONFIG_MANAGE,
      PermissionTypes.FORM_CONFIG_APPLY,
      PermissionTypes.SYSTEM_ADMIN,
      PermissionTypes.CENTER_ADMIN,
      PermissionTypes.ROLE_ADMIN,
    ],
    description: '超级管理员，拥有所有权限',
  },
  {
    role: UserRoles.CENTER_ADMIN,
    permissions: [
      PermissionTypes.FIELD_CONFIG_VIEW,
      PermissionTypes.FIELD_CONFIG_EDIT,
      PermissionTypes.FIELD_CONFIG_CREATE,
      PermissionTypes.FORM_CONFIG_MANAGE,
      PermissionTypes.FORM_CONFIG_APPLY,
      PermissionTypes.CENTER_ADMIN,
      PermissionTypes.ROLE_MANAGER,
    ],
    description: '体检中心管理员，可管理本中心的字段配置',
  },
  {
    role: UserRoles.DEPARTMENT_MANAGER,
    permissions: [
      PermissionTypes.FIELD_CONFIG_VIEW,
      PermissionTypes.FIELD_CONFIG_EDIT,
      PermissionTypes.FORM_CONFIG_APPLY,
      PermissionTypes.ROLE_MANAGER,
    ],
    description: '部门管理员，可查看和编辑字段配置',
  },
  {
    role: UserRoles.OPERATOR,
    permissions: [
      PermissionTypes.FIELD_CONFIG_VIEW,
      PermissionTypes.FORM_CONFIG_APPLY,
      PermissionTypes.ROLE_OPERATOR,
    ],
    description: '操作员，可查看和应用字段配置',
  },
  {
    role: UserRoles.VIEWER,
    permissions: [
      PermissionTypes.FIELD_CONFIG_VIEW,
    ],
    description: '查看者，只能查看字段配置',
  },
];

/**
 * 权限管理器类
 */
export class PermissionManager {
  private userInfo: UserInfo | null = null;
  private permissionConfig: PermissionConfig[] = DEFAULT_PERMISSION_CONFIG;

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo: UserInfo) {
    this.userInfo = userInfo;
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): UserInfo | null {
    return this.userInfo;
  }

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(permission: PermissionTypes): boolean {
    if (!this.userInfo) {
      console.warn('用户信息未设置，权限检查失败');
      return false;
    }

    // 直接检查用户权限列表
    if (this.userInfo.permissions.includes(permission)) {
      return true;
    }

    // 通过角色检查权限
    return this.userInfo.roles.some(role => {
      const roleConfig = this.permissionConfig.find(config => config.role === role);
      return roleConfig?.permissions.includes(permission) || false;
    });
  }

  /**
   * 检查用户是否有任一权限
   */
  hasAnyPermission(permissions: PermissionTypes[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * 检查用户是否有所有权限
   */
  hasAllPermissions(permissions: PermissionTypes[]): boolean {
    return permissions.every(permission => this.hasPermission(permission));
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(role: UserRoles): boolean {
    if (!this.userInfo) {
      return false;
    }
    return this.userInfo.roles.includes(role);
  }

  /**
   * 检查用户是否有任一角色
   */
  hasAnyRole(roles: UserRoles[]): boolean {
    return roles.some(role => this.hasRole(role));
  }

  /**
   * 检查是否是管理员
   */
  isAdmin(): boolean {
    return this.hasAnyRole([UserRoles.SUPER_ADMIN, UserRoles.CENTER_ADMIN]);
  }

  /**
   * 检查是否是超级管理员
   */
  isSuperAdmin(): boolean {
    return this.hasRole(UserRoles.SUPER_ADMIN);
  }

  /**
   * 检查是否是中心管理员
   */
  isCenterAdmin(): boolean {
    return this.hasRole(UserRoles.CENTER_ADMIN);
  }

  /**
   * 检查是否可以配置字段
   */
  canConfigureFields(): boolean {
    return this.hasAnyPermission([
      PermissionTypes.FIELD_CONFIG_EDIT,
      PermissionTypes.FIELD_CONFIG_CREATE,
      PermissionTypes.FORM_CONFIG_MANAGE,
    ]);
  }

  /**
   * 检查是否可以查看字段配置
   */
  canViewFieldConfig(): boolean {
    return this.hasPermission(PermissionTypes.FIELD_CONFIG_VIEW);
  }

  /**
   * 检查是否可以删除字段配置
   */
  canDeleteFieldConfig(): boolean {
    return this.hasPermission(PermissionTypes.FIELD_CONFIG_DELETE);
  }

  /**
   * 检查是否可以创建字段配置
   */
  canCreateFieldConfig(): boolean {
    return this.hasPermission(PermissionTypes.FIELD_CONFIG_CREATE);
  }

  /**
   * 检查是否可以编辑字段配置
   */
  canEditFieldConfig(): boolean {
    return this.hasPermission(PermissionTypes.FIELD_CONFIG_EDIT);
  }

  /**
   * 检查是否可以管理表单配置
   */
  canManageFormConfig(): boolean {
    return this.hasPermission(PermissionTypes.FORM_CONFIG_MANAGE);
  }

  /**
   * 检查是否可以应用表单配置
   */
  canApplyFormConfig(): boolean {
    return this.hasPermission(PermissionTypes.FORM_CONFIG_APPLY);
  }

  /**
   * 获取用户的所有权限
   */
  getUserPermissions(): PermissionTypes[] {
    if (!this.userInfo) {
      return [];
    }

    const permissions = new Set<PermissionTypes>(this.userInfo.permissions);

    // 添加角色权限
    this.userInfo.roles.forEach(role => {
      const roleConfig = this.permissionConfig.find(config => config.role === role);
      if (roleConfig) {
        roleConfig.permissions.forEach(permission => permissions.add(permission));
      }
    });

    return Array.from(permissions);
  }

  /**
   * 获取权限描述
   */
  getPermissionDescription(permission: PermissionTypes): string {
    const descriptions: Record<PermissionTypes, string> = {
      [PermissionTypes.FIELD_CONFIG_VIEW]: '查看字段配置',
      [PermissionTypes.FIELD_CONFIG_EDIT]: '编辑字段配置',
      [PermissionTypes.FIELD_CONFIG_DELETE]: '删除字段配置',
      [PermissionTypes.FIELD_CONFIG_CREATE]: '创建字段配置',
      [PermissionTypes.FORM_CONFIG_MANAGE]: '管理表单配置',
      [PermissionTypes.FORM_CONFIG_APPLY]: '应用表单配置',
      [PermissionTypes.SYSTEM_ADMIN]: '系统管理',
      [PermissionTypes.CENTER_ADMIN]: '中心管理',
      [PermissionTypes.ROLE_ADMIN]: '角色管理',
      [PermissionTypes.ROLE_MANAGER]: '角色管理员',
      [PermissionTypes.ROLE_OPERATOR]: '操作员',
    };

    return descriptions[permission] || permission;
  }

  /**
   * 设置权限配置
   */
  setPermissionConfig(config: PermissionConfig[]) {
    this.permissionConfig = config;
  }

  /**
   * 获取权限配置
   */
  getPermissionConfig(): PermissionConfig[] {
    return this.permissionConfig;
  }
}

/**
 * 全局权限管理器实例
 */
export const permissionManager = new PermissionManager();

/**
 * 权限检查装饰器工厂
 */
export function requirePermission(permission: PermissionTypes) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      if (!permissionManager.hasPermission(permission)) {
        console.warn(`权限不足，需要权限: ${permission}`);
        throw new Error(`权限不足，需要权限: ${permissionManager.getPermissionDescription(permission)}`);
      }
      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * 角色检查装饰器工厂
 */
export function requireRole(role: UserRoles) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      if (!permissionManager.hasRole(role)) {
        console.warn(`角色不足，需要角色: ${role}`);
        throw new Error(`角色不足，需要角色: ${role}`);
      }
      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * 从用户存储或API获取用户信息的工具函数
 */
export async function loadUserInfo(): Promise<UserInfo | null> {
  try {
    // 这里应该从实际的用户存储或API获取用户信息
    // 示例实现，实际应该根据项目的用户管理系统来实现

    // 从localStorage获取用户信息（示例）
    const userStr = localStorage.getItem('userInfo');
    if (userStr) {
      const user = JSON.parse(userStr);
      return {
        id: user.id || 'unknown',
        username: user.username || 'unknown',
        roles: user.roles || [UserRoles.VIEWER],
        permissions: user.permissions || [],
        centerId: user.centerId,
        centerName: user.centerName,
        departmentId: user.departmentId,
        departmentName: user.departmentName,
      };
    }

    // 如果没有用户信息，返回默认的查看者权限
    return {
      id: 'guest',
      username: 'guest',
      roles: [UserRoles.VIEWER],
      permissions: [PermissionTypes.FIELD_CONFIG_VIEW],
    };
  } catch (error) {
    console.error('加载用户信息失败:', error);
    return null;
  }
}

/**
 * 初始化权限管理器
 */
export async function initPermissionManager(): Promise<void> {
  const userInfo = await loadUserInfo();
  if (userInfo) {
    permissionManager.setUserInfo(userInfo);
    console.log('权限管理器初始化成功:', userInfo);
  } else {
    console.warn('权限管理器初始化失败，用户信息为空');
  }
}
