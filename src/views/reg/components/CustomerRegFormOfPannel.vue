<template>
  <div class="customer-reg-form-enhanced">
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" @finish="handleSubmit">
      <!-- 基础信息区域 -->
      <div class="form-section">
        <a-typography-title :level="5">基础信息</a-typography-title>

        <!-- 必填字段 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="姓名" name="name" :rules="[{ required: true, message: '请输入姓名' }]">
              <a-input v-model:value="formData.name" placeholder="请输入姓名" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="身份证号" name="idCard" :rules="[{ required: true, message: '请输入身份证号' }]">
              <a-input v-model:value="formData.idCard" placeholder="请输入身份证号" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 动态字段渲染区域 -->
      <div class="form-section">
        <a-typography-title :level="5">
          档案及体检信息
          <a-button v-if="canShowConfigButton" type="link" size="small" @click="openFieldConfig" style="margin-left: 8px">
            <Icon icon="ant-design:setting-outlined" />
            配置字段
          </a-button>
        </a-typography-title>

        <!-- 使用增强的表单渲染器 -->
        <EnhancedFormRenderer
          v-model="formData"
          :fieldConfig="fieldConfig"
          :formType="FormTypes.CUSTOMER_REG"
          :disabled="disabled"
          :validateInfos="validateInfos"
          :showConfigButton="false"
          :hasConfigPermission="canShowConfigButton"
          :selectOptionsMap="selectOptionsMap"
          :searchExtrasMap="searchExtrasMap"
          :searchLoadingMap="searchLoadingMap"
          @field-change="handleFieldChange"
          @field-search="handleFieldSearch"
          @config-success="handleConfigSuccess"
        />
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <a-space>
          <a-button type="primary" html-type="submit" :loading="submitLoading">
            <Icon icon="ant-design:save-outlined" />
            保存
          </a-button>
          <a-button @click="handleReset">
            <Icon icon="ant-design:reload-outlined" />
            重置
          </a-button>
        </a-space>
      </div>
    </a-form>

    <!-- 字段配置弹窗 -->
    <FormFieldConfigModal ref="configModalRef" @success="handleConfigSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { Form, message } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import EnhancedFormRenderer from './EnhancedFormRenderer.vue';
  import FormFieldConfigModal from './FormFieldConfigModal.vue';

  // 导入新的配置系统
  import { FieldKeys, FieldDisplayLocation } from '../config/FieldDefinitions';
  import { FormTypes } from '../config/FieldConfigUtils';
  import { useFieldConfig } from '../composables/useFieldConfig';
  import { useFieldConfigPermissions } from '../composables/usePermissions';

  interface Props {
    disabled?: boolean;
    initialData?: Record<string, any>;
  }

  interface Emits {
    (e: 'submit', data: Record<string, any>): void;
    (e: 'reset'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    initialData: () => ({}),
  });

  const emit = defineEmits<Emits>();

  // 表单引用和验证
  const formRef = ref();
  const { validate, validateInfos, resetFields } = Form.useForm();

  // 权限管理
  const { canShowConfigButton, canModifyFieldConfig } = useFieldConfigPermissions();

  // 字段配置管理
  const {
    isLoading: configLoading,
    fieldConfig,
    loadError,
    loadFieldConfig,
    saveFieldConfig,
    isFieldInLocation,
  } = useFieldConfig({
    formType: FormTypes.CUSTOMER_REG,
    enableCache: true,
    fallbackToDefault: true,
  });

  // 表单数据
  const formData = reactive({
    name: '',
    idCard: '',
    nation: '',
    bloodType: '',
    countryCode: '',
    postCode: '',
    eduLevel: '',
    marriageStatus: '',
    customerCategory: '',
    pcaCode: [],
    address: '',
    emergencyContact: '',
    emergencyPhone: '',
    isPregnancyPrep: false,
    healthNo: '',
    examCardNo: '',
    workYears: null,
    companyName: '',
    companyId: '',
    companyDeptId: '',
    belongCompany: '',
    department: '',
    supplyFlag: false,
    prePayFlag: false,
    reExamStatus: false,
    reExamRemark: '',
    recipeTitle: '',
    originalIdCard: '',
    relationWithOriginal: '',
    introducer: '',
    secretLevel: '',
    remark: '',
    ...props.initialData,
  });

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: '请输入姓名' }],
    idCard: [{ required: true, message: '请输入身份证号' }],
  };

  // 状态管理
  const submitLoading = ref(false);
  const configModalRef = ref();

  // 选择框选项映射
  const selectOptionsMap = ref<Record<FieldKeys, Array<{ label: string; value: any }>>>({
    [FieldKeys.COMPANY_DEPT_ID]: [], // 部门选项会动态加载
  });

  // 搜索额外信息映射
  const searchExtrasMap = ref<Record<FieldKeys, string>>({
    [FieldKeys.ORIGINAL_ID_CARD]: '', // 原检人员信息
  });

  // 搜索加载状态映射
  const searchLoadingMap = ref<Record<FieldKeys, boolean>>({
    [FieldKeys.ORIGINAL_ID_CARD]: false,
  });

  // 处理字段变化
  const handleFieldChange = (fieldKey: FieldKeys, value: any, option?: any) => {
    console.log('字段变化:', fieldKey, value, option);

    // 处理特殊字段的联动逻辑
    switch (fieldKey) {
      case FieldKeys.COMPANY_ID:
        // 当选择单位时，加载部门列表
        loadDepartments(value);
        break;
      case FieldKeys.COUNTRY_CODE:
        // 处理国籍变化
        handleCountryChange(value, option);
        break;
      // 可以添加更多字段的特殊处理逻辑
    }
  };

  // 处理字段搜索
  const handleFieldSearch = (fieldKey: FieldKeys, value: string) => {
    console.log('字段搜索:', fieldKey, value);

    switch (fieldKey) {
      case FieldKeys.ORIGINAL_ID_CARD:
        searchOriginalCustomer(value);
        break;
    }
  };

  // 加载部门列表
  const loadDepartments = async (companyId: string) => {
    try {
      // 这里应该调用实际的API
      // const departments = await getDepartmentsByCompanyId(companyId);
      // selectOptionsMap.value[FieldKeys.COMPANY_DEPT_ID] = departments;

      // 模拟数据
      selectOptionsMap.value[FieldKeys.COMPANY_DEPT_ID] = [
        { label: '行政部', value: '1' },
        { label: '技术部', value: '2' },
        { label: '财务部', value: '3' },
      ];
    } catch (error) {
      console.error('加载部门列表失败:', error);
      message.error('加载部门列表失败');
    }
  };

  // 处理国籍变化
  const handleCountryChange = (value: any, option: any) => {
    console.log('国籍变化:', value, option);
    // 可以添加国籍变化的处理逻辑
  };

  // 搜索原检人员
  const searchOriginalCustomer = async (idCard: string) => {
    if (!idCard.trim()) {
      return;
    }

    try {
      searchLoadingMap.value[FieldKeys.ORIGINAL_ID_CARD] = true;

      // 这里应该调用实际的API
      // const customer = await searchCustomerByIdCard(idCard);
      // searchExtrasMap.value[FieldKeys.ORIGINAL_ID_CARD] = customer ? customer.name : '未找到相关人员';

      // 模拟搜索
      setTimeout(() => {
        searchExtrasMap.value[FieldKeys.ORIGINAL_ID_CARD] = '张三 (模拟数据)';
        searchLoadingMap.value[FieldKeys.ORIGINAL_ID_CARD] = false;
      }, 1000);
    } catch (error) {
      console.error('搜索原检人员失败:', error);
      message.error('搜索原检人员失败');
      searchLoadingMap.value[FieldKeys.ORIGINAL_ID_CARD] = false;
    }
  };

  // 打开字段配置
  const openFieldConfig = () => {
    if (!canModifyFieldConfig.value) {
      message.warning('您没有权限修改字段配置');
      return;
    }

    configModalRef.value?.open();
  };

  // 配置成功回调
  const handleConfigSuccess = async () => {
    message.success('字段配置已更新');
    // 重新加载配置
    await loadFieldConfig();
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await validate();
      submitLoading.value = true;

      // 这里应该调用实际的保存API
      console.log('提交表单数据:', formData);

      // 模拟提交
      await new Promise((resolve) => setTimeout(resolve, 1000));

      message.success('保存成功');
      emit('submit', { ...formData });
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('请检查表单数据');
    } finally {
      submitLoading.value = false;
    }
  };

  // 重置表单
  const handleReset = () => {
    resetFields();
    Object.assign(formData, props.initialData);
    emit('reset');
  };

  // 组件挂载时加载配置
  onMounted(async () => {
    await loadFieldConfig();

    if (loadError.value) {
      message.warning(`字段配置加载失败: ${loadError.value}`);
    }
  });

  // 暴露方法给父组件
  defineExpose({
    validate,
    resetFields,
    isFieldInLocation,
    openFieldConfig,
  });
</script>

<style scoped>
  .customer-reg-form-enhanced {
    padding: 16px;
  }

  .form-section {
    margin-bottom: 24px;
  }

  .form-actions {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
</style>
