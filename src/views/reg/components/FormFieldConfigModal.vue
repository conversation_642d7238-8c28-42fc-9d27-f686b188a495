<template>
  <a-modal v-model:open="visible" title="折叠区域字段配置" width="800px" :confirm-loading="confirmLoading" @ok="handleSave" @cancel="handleCancel">
    <a-spin :spinning="loading">
      <div class="config-container">
        <!-- 配置基本信息 -->
        <a-card size="small" title="配置信息" class="config-info-card">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="表单类型" v-bind="validateInfos.formType">
                <a-select v-model:value="formData.formType" placeholder="请选择表单类型" @change="handleFormTypeChange">
                  <a-select-option value="customer_reg">客户登记表单</a-select-option>
                  <a-select-option value="company_reg">单位登记表单</a-select-option>
                  <a-select-option value="exam_config">体检配置表单</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否启用">
                <a-switch v-model:checked="formData.isActive" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 字段显示配置区域 -->
        <a-card size="small" title="折叠区域字段配置" class="fields-config-card">
          <template #extra>
            <a-space>
              <a-button size="small" @click="loadDefaultConfig"> <Icon icon="ant-design:reload-outlined" /> 加载默认配置 </a-button>
              <a-button size="small" @click="selectAll"> <Icon icon="ant-design:check-outlined" /> 全选 </a-button>
              <a-button size="small" @click="selectNone"> <Icon icon="ant-design:close-outlined" /> 全不选 </a-button>
            </a-space>
          </template>

          <!-- 字段列表 -->
          <div class="fields-list">
            <div class="field-group-title">选择要在折叠区域显示的字段：</div>
            <div class="fields-grid">
              <template v-for="field in formData.fields" :key="field.fieldKey">
                <div class="field-item" :class="{ 'field-enabled': field.isVisible }">
                  <div class="field-header">
                    <div class="field-info">
                      <a-checkbox v-model:checked="field.isVisible" class="field-checkbox" />
                      <span class="field-name">{{ field.fieldName }}</span>
                      <a-tag v-if="field.groupName" size="small" color="blue">
                        {{ field.groupName }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </a-card>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { Form, message } from 'ant-design-vue';
  import { Icon } from '@/components/Icon';
  import { FormDisplayConfig, FieldDisplayConfig, saveFormDisplayConfig, FORM_TYPES, CUSTOMER_REG_CONFIGURABLE_FIELDS } from '../FormFieldConfig.api';

  const emit = defineEmits(['register', 'success']);

  const visible = ref(false);
  const loading = ref(false);
  const confirmLoading = ref(false);

  const formData = reactive<FormDisplayConfig>({
    configName: '',
    centerId: '',
    centerName: '',
    formType: 'customer_reg',
    isActive: true,
    fields: [],
  });

  // 表单验证
  const useForm = Form.useForm;
  const rules = reactive({
    formType: [{ required: true, message: '请选择表单类型' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, rules);

  // 表单类型变化处理
  const handleFormTypeChange = (formType: string) => {
    loadDefaultConfig();
  };

  // 全选
  const selectAll = () => {
    formData.fields.forEach((field) => {
      field.isVisible = true;
    });
  };

  // 全不选
  const selectNone = () => {
    formData.fields.forEach((field) => {
      field.isVisible = false;
    });
  };

  // 加载默认配置
  const loadDefaultConfig = () => {
    if (formData.formType === FORM_TYPES.CUSTOMER_REG) {
      formData.fields = JSON.parse(JSON.stringify(CUSTOMER_REG_CONFIGURABLE_FIELDS));
    } else {
      formData.fields = [];
    }
    message.success('已加载默认配置');
  };

  // 保存配置
  const handleSave = async () => {
    try {
      await validate();
      confirmLoading.value = true;

      // 设置体检中心信息（实际应该从登录用户信息获取）
      if (!formData.centerId) {
        formData.centerId = 'current_center_id';
        formData.centerName = '当前体检中心';
      }

      await saveFormDisplayConfig(formData);
      message.success('保存成功');
      emit('success');
      handleCancel();
    } catch (error) {
      console.error('保存失败:', error);

      // 如果是唯一约束冲突错误
      if (error?.message?.includes('Duplicate entry') || error?.message?.includes('uk_center_form')) {
        message.error('该表单类型的配置已存在，请刷新页面后重试或联系管理员');
      }
      // 如果是 404 错误，说明后端接口还没有实现
      else if (error?.response?.status === 404) {
        message.warning('后端接口尚未实现，配置暂时无法保存到服务器');
        // 可以选择将配置保存到本地存储作为临时方案
        localStorage.setItem('fieldDisplayConfig', JSON.stringify(formData));
        emit('success');
        handleCancel();
      } else {
        message.error('保存失败：' + (error?.message || '请稍后重试'));
      }
    } finally {
      confirmLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
    resetFields();
    formData.fields = [];
  };

  // 打开弹窗
  const open = (config?: FormDisplayConfig) => {
    visible.value = true;
    if (config) {
      // 复制基本配置信息
      formData.id = config.id;
      formData.configName = config.configName;
      formData.centerId = config.centerId;
      formData.centerName = config.centerName;
      formData.formType = config.formType;
      formData.isActive = config.isActive;

      // 合并字段配置：以完整字段列表为基础，更新显示状态
      const fullFieldList = JSON.parse(JSON.stringify(CUSTOMER_REG_CONFIGURABLE_FIELDS));
      if (config.fields && config.fields.length > 0) {
        // 根据传入的配置更新字段的显示状态
        fullFieldList.forEach(field => {
          const configField = config.fields.find(cf => cf.fieldKey === field.fieldKey);
          if (configField) {
            field.isVisible = configField.isVisible;
          }
        });
      }
      formData.fields = fullFieldList;
    } else {
      // 新建时加载默认配置
      loadDefaultConfig();
    }
  };

  defineExpose({
    open,
  });
</script>

<style scoped>
  .config-container {
    max-height: 600px;
    overflow-y: auto;
  }

  .config-info-card {
    margin-bottom: 16px;
  }

  .fields-config-card {
    margin-bottom: 16px;
  }

  .fields-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
  }

  .field-group-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: #f0f9ff;
    border-left: 3px solid #1890ff;
    border-radius: 4px;
  }

  .field-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 12px;
    background: #fff;
    transition: all 0.2s;
  }

  .field-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  .field-enabled {
    background: #f6ffed;
    border-color: #b7eb8f;
  }

  .field-header {
    display: flex;
    align-items: center;
  }

  .field-info {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .field-checkbox {
    margin: 0;
  }

  .field-name {
    font-weight: 500;
    color: #333;
    flex: 1;
  }
</style>
