# 配置信息传递测试说明

## 🎯 问题修复

已修复 `openFieldConfig` 方法没有传递配置信息的问题。

### ✅ 修复内容

#### 1. **修改了 openFieldConfig 方法**
```typescript
function openFieldConfig() {
  // 构造当前配置信息
  const currentConfig = {
    id: 'current_config',
    configName: '当前字段配置',
    centerId: 'current_center_id',
    centerName: '当前体检中心',
    formType: FORM_TYPES.CUSTOMER_REG,
    isActive: true,
    fields: fieldDisplayConfig.value && fieldDisplayConfig.value.length > 0 
      ? fieldDisplayConfig.value 
      : CUSTOMER_REG_CONFIGURABLE_FIELDS
  };
  
  configModalRef.value?.open(currentConfig);
}
```

#### 2. **优化了配置弹窗的 open 方法**
```typescript
const open = (config?: FormDisplayConfig) => {
  visible.value = true;
  if (config) {
    // 复制基本配置信息
    formData.id = config.id;
    formData.configName = config.configName;
    formData.centerId = config.centerId;
    formData.centerName = config.centerName;
    formData.formType = config.formType;
    formData.isActive = config.isActive;
    
    // 合并字段配置：以完整字段列表为基础，更新显示状态
    const fullFieldList = JSON.parse(JSON.stringify(CUSTOMER_REG_CONFIGURABLE_FIELDS));
    if (config.fields && config.fields.length > 0) {
      fullFieldList.forEach(field => {
        const configField = config.fields.find(cf => cf.fieldKey === field.fieldKey);
        if (configField) {
          field.isVisible = configField.isVisible;
        }
      });
    }
    formData.fields = fullFieldList;
  } else {
    loadDefaultConfig();
  }
};
```

## 🔧 修复的问题

### **问题1：配置信息丢失**
- **原因**：`openFieldConfig()` 没有传递当前配置
- **修复**：传递包含当前字段配置状态的完整配置对象

### **问题2：字段状态不同步**
- **原因**：弹窗中的字段状态与当前表单状态不一致
- **修复**：合并当前配置状态到完整字段列表中

### **问题3：配置覆盖问题**
- **原因**：直接赋值可能导致字段配置不完整
- **修复**：以完整字段列表为基础，只更新显示状态

## 🎮 测试步骤

### **步骤1：验证配置传递**
1. 打开客户登记表单
2. 点击"配置字段"按钮
3. 检查弹窗中的字段勾选状态是否与当前配置一致
4. 在浏览器控制台查看日志：`打开配置弹窗，当前配置:`

### **步骤2：验证配置同步**
1. 在配置弹窗中修改一些字段的勾选状态
2. 保存配置
3. 再次打开配置弹窗
4. 确认之前的修改已经保存并正确显示

### **步骤3：验证字段完整性**
1. 打开配置弹窗
2. 确认所有28个可配置字段都显示在列表中
3. 确认每个字段的勾选状态正确反映当前配置

### **步骤4：验证默认配置**
1. 如果是第一次使用（没有保存过配置）
2. 打开配置弹窗
3. 应该显示默认配置状态

## 🔍 调试信息

### **浏览器控制台日志**
打开配置弹窗时会输出：
```
打开配置弹窗，当前配置: {
  id: "current_config",
  configName: "当前字段配置",
  centerId: "current_center_id",
  centerName: "当前体检中心",
  formType: "customer_reg",
  isActive: true,
  fields: [...]
}
```

### **检查要点**
1. **fields 数组**：应该包含28个字段配置
2. **isVisible 状态**：应该反映当前的显示配置
3. **字段完整性**：确保所有字段都在列表中

## 🎯 预期效果

### **正确的行为**
- ✅ 打开配置弹窗时显示当前配置状态
- ✅ 字段的勾选状态与表单显示状态一致
- ✅ 修改配置后再次打开弹窗，显示最新配置
- ✅ 所有字段都正确显示在配置列表中

### **配置流程**
1. **打开弹窗** → 显示当前配置状态
2. **修改配置** → 勾选/取消勾选字段
3. **保存配置** → 配置保存到数据库
4. **配置生效** → 表单字段布局立即更新
5. **再次打开** → 显示最新的配置状态

## 🚨 如果还有问题

如果配置信息仍然没有正确传递，请检查：

1. **控制台日志**：查看是否有 `打开配置弹窗，当前配置:` 的日志输出
2. **字段数据**：检查 `fieldDisplayConfig.value` 是否包含正确的配置数据
3. **网络请求**：确认配置加载的 API 请求是否成功
4. **数据格式**：确认配置数据的格式是否正确

## 💡 技术细节

### **配置合并逻辑**
1. 以完整的字段列表（CUSTOMER_REG_CONFIGURABLE_FIELDS）为基础
2. 根据当前配置更新每个字段的 `isVisible` 状态
3. 确保所有字段都在配置列表中，避免遗漏

### **数据流向**
```
表单加载 → loadFieldDisplayConfig() → fieldDisplayConfig.value
↓
点击配置按钮 → openFieldConfig() → 构造currentConfig
↓
打开弹窗 → open(currentConfig) → 合并字段配置
↓
显示配置界面 → 用户修改 → 保存配置
↓
配置生效 → handleConfigSuccess() → 重新加载配置
```

现在配置信息应该能够正确传递到配置弹窗中了！
