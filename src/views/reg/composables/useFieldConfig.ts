/**
 * 字段配置管理组合式函数
 * 提供统一的字段配置加载、保存、验证等功能
 */

import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  FieldDisplayConfig,
  FormDisplayConfig,
  FieldKeys,
  FieldDisplayLocation,
} from '../config/FieldDefinitions';
import {
  FormTypes,
  customerRegFieldManager,
  createFieldConfigManager,
} from '../config/FieldConfigUtils';
import {
  getActiveFormDisplayConfig,
  saveFormDisplayConfig,
  getDefaultFieldConfig,
  validateFieldConfig,
  migrateOldFieldConfig,
} from '../FormFieldConfig.api';

/**
 * 配置加载策略枚举
 */
export enum ConfigLoadStrategy {
  API_FIRST = 'api_first',           // API优先
  LOCAL_FIRST = 'local_first',       // 本地存储优先
  DEFAULT_ONLY = 'default_only',     // 仅使用默认配置
}

/**
 * 配置加载选项
 */
export interface ConfigLoadOptions {
  strategy?: ConfigLoadStrategy;
  formType?: FormTypes;
  centerId?: string;
  enableCache?: boolean;
  cacheKey?: string;
  fallbackToDefault?: boolean;
  validateConfig?: boolean;
}

/**
 * 配置加载结果
 */
export interface ConfigLoadResult {
  success: boolean;
  config: FieldDisplayConfig[];
  source: 'api' | 'local' | 'default' | 'cache';
  errors?: string[];
  warnings?: string[];
}

/**
 * 字段配置管理组合式函数
 */
export function useFieldConfig(options: ConfigLoadOptions = {}) {
  // 默认选项
  const defaultOptions: Required<ConfigLoadOptions> = {
    strategy: ConfigLoadStrategy.API_FIRST,
    formType: FormTypes.CUSTOMER_REG,
    centerId: '',
    enableCache: true,
    cacheKey: 'fieldDisplayConfig',
    fallbackToDefault: true,
    validateConfig: true,
  };

  const opts = { ...defaultOptions, ...options };

  // 响应式状态
  const isLoading = ref(false);
  const fieldConfig = ref<FieldDisplayConfig[]>([]);
  const loadError = ref<string | null>(null);
  const loadWarnings = ref<string[]>([]);
  const lastLoadSource = ref<'api' | 'local' | 'default' | 'cache'>('default');

  // 字段配置管理器
  const fieldManager = computed(() => createFieldConfigManager(opts.formType));

  // 计算属性
  const outsideFields = computed(() => fieldManager.value.getOutsideFields(fieldConfig.value));
  const collapseFields = computed(() => fieldManager.value.getCollapseFields(fieldConfig.value));
  const hiddenFields = computed(() => fieldManager.value.getHiddenFields(fieldConfig.value));

  /**
   * 从缓存加载配置
   */
  const loadFromCache = (): ConfigLoadResult | null => {
    if (!opts.enableCache) {
      return null;
    }

    try {
      const cacheKey = `${opts.cacheKey}_${opts.formType}_${opts.centerId}`;
      const cached = sessionStorage.getItem(cacheKey);
      
      if (cached) {
        const config = JSON.parse(cached);
        
        if (opts.validateConfig) {
          const validation = fieldManager.value.validateFieldConfig(config);
          if (!validation.isValid) {
            console.warn('缓存的配置验证失败:', validation.errors);
            return null;
          }
        }

        return {
          success: true,
          config,
          source: 'cache',
        };
      }
    } catch (error) {
      console.warn('从缓存加载配置失败:', error);
    }

    return null;
  };

  /**
   * 保存配置到缓存
   */
  const saveToCache = (config: FieldDisplayConfig[]) => {
    if (!opts.enableCache) {
      return;
    }

    try {
      const cacheKey = `${opts.cacheKey}_${opts.formType}_${opts.centerId}`;
      sessionStorage.setItem(cacheKey, JSON.stringify(config));
    } catch (error) {
      console.warn('保存配置到缓存失败:', error);
    }
  };

  /**
   * 从API加载配置
   */
  const loadFromAPI = async (): Promise<ConfigLoadResult> => {
    try {
      const config = await getActiveFormDisplayConfig({
        formType: opts.formType,
      });

      if (config && config.fields && config.fields.length > 0) {
        let processedConfig = config.fields;

        // 验证配置
        if (opts.validateConfig) {
          const validation = fieldManager.value.validateFieldConfig(processedConfig);
          if (!validation.isValid) {
            console.warn('API配置验证失败:', validation.errors);
            return {
              success: false,
              config: [],
              source: 'api',
              errors: validation.errors,
            };
          }
        }

        // 迁移旧版本配置
        if (processedConfig.some(field => field.isVisible !== undefined && !field.displayLocation)) {
          processedConfig = fieldManager.value.migrateOldConfig(processedConfig);
        }

        return {
          success: true,
          config: processedConfig,
          source: 'api',
        };
      } else {
        return {
          success: false,
          config: [],
          source: 'api',
          warnings: ['API返回的配置为空'],
        };
      }
    } catch (error: any) {
      console.warn('从API加载配置失败:', error);
      
      return {
        success: false,
        config: [],
        source: 'api',
        errors: [error.message || '加载配置失败'],
      };
    }
  };

  /**
   * 从本地存储加载配置
   */
  const loadFromLocal = (): ConfigLoadResult => {
    try {
      const localConfig = localStorage.getItem(opts.cacheKey);
      
      if (localConfig) {
        const parsedConfig = JSON.parse(localConfig);
        
        if (parsedConfig.fields && Array.isArray(parsedConfig.fields)) {
          let config = parsedConfig.fields;

          // 验证配置
          if (opts.validateConfig) {
            const validation = fieldManager.value.validateFieldConfig(config);
            if (!validation.isValid) {
              console.warn('本地配置验证失败:', validation.errors);
              return {
                success: false,
                config: [],
                source: 'local',
                errors: validation.errors,
              };
            }
          }

          // 迁移旧版本配置
          if (config.some((field: any) => field.isVisible !== undefined && !field.displayLocation)) {
            config = fieldManager.value.migrateOldConfig(config);
          }

          return {
            success: true,
            config,
            source: 'local',
          };
        }
      }

      return {
        success: false,
        config: [],
        source: 'local',
        warnings: ['本地存储中没有找到有效配置'],
      };
    } catch (error) {
      console.warn('从本地存储加载配置失败:', error);
      
      return {
        success: false,
        config: [],
        source: 'local',
        errors: ['本地存储配置解析失败'],
      };
    }
  };

  /**
   * 获取默认配置
   */
  const getDefaultConfig = (): ConfigLoadResult => {
    try {
      const config = fieldManager.value.createDefaultFieldDisplayConfig();
      
      return {
        success: true,
        config,
        source: 'default',
      };
    } catch (error) {
      console.error('获取默认配置失败:', error);
      
      return {
        success: false,
        config: [],
        source: 'default',
        errors: ['获取默认配置失败'],
      };
    }
  };

  /**
   * 加载字段配置
   */
  const loadFieldConfig = async (): Promise<ConfigLoadResult> => {
    isLoading.value = true;
    loadError.value = null;
    loadWarnings.value = [];

    try {
      let result: ConfigLoadResult;

      // 根据策略加载配置
      switch (opts.strategy) {
        case ConfigLoadStrategy.API_FIRST:
          // 先尝试从缓存加载
          result = loadFromCache() || { success: false, config: [], source: 'cache' };
          
          if (!result.success) {
            // 尝试从API加载
            result = await loadFromAPI();
            
            if (!result.success && opts.fallbackToDefault) {
              // 尝试从本地存储加载
              const localResult = loadFromLocal();
              if (localResult.success) {
                result = localResult;
              } else {
                // 最后使用默认配置
                result = getDefaultConfig();
              }
            }
          }
          break;

        case ConfigLoadStrategy.LOCAL_FIRST:
          // 先尝试从本地存储加载
          result = loadFromLocal();
          
          if (!result.success) {
            // 尝试从API加载
            result = await loadFromAPI();
            
            if (!result.success && opts.fallbackToDefault) {
              result = getDefaultConfig();
            }
          }
          break;

        case ConfigLoadStrategy.DEFAULT_ONLY:
          result = getDefaultConfig();
          break;

        default:
          result = getDefaultConfig();
      }

      // 更新状态
      if (result.success) {
        fieldConfig.value = result.config;
        lastLoadSource.value = result.source;
        
        // 保存到缓存
        if (result.source !== 'cache') {
          saveToCache(result.config);
        }
        
        console.log(`字段配置加载成功，来源: ${result.source}`, result.config);
      } else {
        loadError.value = result.errors?.join('; ') || '加载配置失败';
        
        if (opts.fallbackToDefault) {
          const defaultResult = getDefaultConfig();
          if (defaultResult.success) {
            fieldConfig.value = defaultResult.config;
            lastLoadSource.value = defaultResult.source;
            console.log('使用默认配置作为后备方案');
          }
        }
      }

      if (result.warnings) {
        loadWarnings.value = result.warnings;
      }

      return result;
    } catch (error: any) {
      console.error('加载字段配置时发生错误:', error);
      loadError.value = error.message || '加载配置时发生未知错误';
      
      if (opts.fallbackToDefault) {
        const defaultResult = getDefaultConfig();
        if (defaultResult.success) {
          fieldConfig.value = defaultResult.config;
          lastLoadSource.value = defaultResult.source;
        }
      }

      return {
        success: false,
        config: fieldConfig.value,
        source: lastLoadSource.value,
        errors: [error.message || '加载配置时发生未知错误'],
      };
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 保存字段配置
   */
  const saveFieldConfig = async (config: FieldDisplayConfig[]): Promise<boolean> => {
    try {
      // 验证配置
      if (opts.validateConfig) {
        const validation = fieldManager.value.validateFieldConfig(config);
        if (!validation.isValid) {
          message.error(`配置验证失败: ${validation.errors.join('; ')}`);
          return false;
        }
      }

      // 构造表单配置
      const formConfig: FormDisplayConfig = {
        configName: '当前字段配置',
        centerId: opts.centerId || 'current_center',
        centerName: '当前体检中心',
        formType: opts.formType,
        isActive: true,
        fields: config,
      };

      // 保存到API
      await saveFormDisplayConfig(formConfig);
      
      // 更新本地状态
      fieldConfig.value = config;
      
      // 保存到缓存
      saveToCache(config);
      
      // 保存到本地存储作为备份
      try {
        localStorage.setItem(opts.cacheKey, JSON.stringify({ fields: config }));
      } catch (error) {
        console.warn('保存到本地存储失败:', error);
      }

      message.success('字段配置保存成功');
      return true;
    } catch (error: any) {
      console.error('保存字段配置失败:', error);
      message.error(`保存配置失败: ${error.message || '未知错误'}`);
      return false;
    }
  };

  /**
   * 重置为默认配置
   */
  const resetToDefault = () => {
    const defaultResult = getDefaultConfig();
    if (defaultResult.success) {
      fieldConfig.value = defaultResult.config;
      lastLoadSource.value = defaultResult.source;
      message.success('已重置为默认配置');
    } else {
      message.error('重置配置失败');
    }
  };

  /**
   * 清除缓存
   */
  const clearCache = () => {
    try {
      const cacheKey = `${opts.cacheKey}_${opts.formType}_${opts.centerId}`;
      sessionStorage.removeItem(cacheKey);
      localStorage.removeItem(opts.cacheKey);
      message.success('缓存已清除');
    } catch (error) {
      console.warn('清除缓存失败:', error);
      message.error('清除缓存失败');
    }
  };

  /**
   * 检查字段是否在指定位置显示
   */
  const isFieldInLocation = (fieldKey: FieldKeys, location: FieldDisplayLocation): boolean => {
    return fieldManager.value.isFieldInLocation(fieldKey, location, fieldConfig.value);
  };

  /**
   * 更新字段显示位置
   */
  const updateFieldLocation = (fieldKey: FieldKeys, location: FieldDisplayLocation) => {
    fieldConfig.value = fieldManager.value.updateFieldLocation(fieldKey, location, fieldConfig.value);
  };

  return {
    // 状态
    isLoading,
    fieldConfig,
    loadError,
    loadWarnings,
    lastLoadSource,

    // 计算属性
    outsideFields,
    collapseFields,
    hiddenFields,

    // 方法
    loadFieldConfig,
    saveFieldConfig,
    resetToDefault,
    clearCache,
    isFieldInLocation,
    updateFieldLocation,

    // 工具方法
    fieldManager,
  };
}
